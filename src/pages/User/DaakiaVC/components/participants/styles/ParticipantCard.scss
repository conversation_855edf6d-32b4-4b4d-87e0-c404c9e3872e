@import "../../../styles/variables";

.post-card {
  background-color: #000;
  display: flex;
  max-width: 100%;
  gap: 20px;
  justify-content: space-between;
  padding: 2px 16px;
  padding-right: 8px;
  // margin: 5px;
}

.user-info {
  display: flex;
  gap: 8px;
  font-weight: 400;
  width: 100%;
  .ant-avatar {
    object-fit: cover;
    width: 38px;
    height: 34px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .user-details {
    display: flex;
    flex-direction: column;
    width: 100%;
    .ptc-icos{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .username {
      color: #fff;
      font: 12px $font;
      
      &-input{
        border: none;
        padding: 0;
        border: 1px solid white;
      }
    }
    .timestamp {
      color: #c0c0c0;
      // margin-top: 5px;
      font: 10px $font;
    }
  }
}
.action-icons {
  display: flex;
  gap: 7px;
  padding: 2px 0;
  margin-right: 5px;
  .icon {
    object-fit: contain;
    display: flex;
    align-items: center;
    // align-items: flex-start;
    height: auto;
    .ppo-icon-participant{
      display: flex;
      // align-items: flex-start;
    }
    .ppo-icon{
      // padding: 0 4px;
      display: flex;
    }
    svg{
      width: 17px;
      height: auto;
    }
    &:nth-child(3){
      svg{
        height: 14px !important;
      }
    }
  }
}
.action-icons-admin{
  margin: 0;
}


.icon-1 {
  aspect-ratio: 0.83;
  width: 100%;
  flex: 1;
}
.icon-2 {
  aspect-ratio: 0.93;
  width: 28px;
  font-size: 20px;
}
.icon-3 {
  aspect-ratio: 0.33;
  width: 7px;
  margin: auto 0;
}
@media (max-width: 768px) {
  .post-card {
    gap: 10px;
  }
  .action-icons {
    justify-content: flex-end;
  }
}
// WaitingParticipantCard.js
.post-card-waiting{
  display: flex;
  flex-direction: column;
  padding: 13px 16px;
  .wpc-buttons{
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    button{
      padding: 0 6px;
      border-radius: 4px;
    }
    button:nth-child(1){
      color: white;
      border: 1px solid #fff;
      font-family: $font;
    }
    button:nth-child(2){
      color: #0A84FF;
      border: 1px solid #0A84FF;
      font-family: $font;
    }
  }
}
.wpc-user-details{
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.username{
  color: white;
  font-family: $font;
}
.lk-screen-share-overlay{
  position: absolute;
  background-color: black;
  border: 1px solid white;
  border-radius: 6px;
  padding: 1rem;
  z-index: 100;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  span{
    display: flex;
    text-align: center;
  }
}