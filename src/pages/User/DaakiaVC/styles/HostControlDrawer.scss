@import "../styles/variables";

.hc {
  display: flex;
  flex-direction: column;
  padding: 4px;
  margin-top: 0.7rem;

  &-comps{
    margin-left: 1.2rem;

    &-heading{
      font-size: 18px !important;
      margin-bottom: 0.4rem;
    }

    &-desc{
      font-size: 12px !important;
    }
  }
  &-subhead {
    font-family: $font;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.62px;
    text-align: left;
  }
  &-component {
    display: flex;
    flex-direction: column;
    margin-top: 1rem;
    &-switch {
      display: flex;
      width: 100%;
      justify-content: space-between;
      p {
        font-family: $font;
        font-size: 20px;
        font-weight: 600;
        line-height: 26.6px;
        text-align: left;
      }
      .ant-switch{
        background-color: #d9d9d9;
        &.ant-switch-checked{
          background-color: #30CA9F;
        }
      }
    }
    > p {
      font-family: $font;
      font-size: 14px;
      font-weight: 400;
      line-height: 18.62px;
      text-align: left;
    }
  }
  hr{
    opacity: 1;
  }
  .component-2{
    margin-top: 0.5rem;
  }
}
