.record-popover {
  padding: 0;
  .ant-popover-inner-content {
    width: 100%;
  }
}
.setting-control-button-popover {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  .ant-popover-inner-content {
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .ant-popover-inner {
    border-radius: 10px;
  }
}
.recording-button{
  background-color: transparent;
  border: none;
  &:hover{
    background-color: transparent;
    border: none;
  }
}
.settings-menu-item {
  color: white;
  padding: 2% 4%;
  display: flex;
  justify-content: start;
  cursor: pointer;
  align-items: center;
  position: relative;
  // height: 3em;
  // margin: 5px;
  gap: 0.7em;
  width: 100%;
  .permission-requests{
    position: absolute;
    // top: -0.5rem;
    right: -0.5rem;
    background-color: #3b60e4;
    color: white;
    padding: 0rem 0.5rem;
    border-radius: 50%;
    box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);
    font-size: 12px;
    z-index: 10;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &.permissions-button{
    .settings-menu-inner-icon{
      svg{
        color: white !important;
      }
    }
  }
  &:hover {
    background-color: #eff1f4;
    color: #000;
    border-radius: 6px;
    .settings-menu-inner-icon {
      svg {
        color: #000;
        filter: saturate(0%) hue-rotate(200deg) brightness(50%) contrast(90%);
      }
    }
  }
  .settings-menu-inner-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 20px;
    svg {
      color: black;
      width: 23px;
      height: 23px;
    }
  }

  // Settings Control Icons
  .ve-icon {
    svg {
      width: 19px !important;
      height: 19px !important;
    }
  }
  .live-cap-icon {
    svg {
      width: 20px !important;
      height: 20px !important;
    }
  }
  .troubleshoot-icon{
    svg{
      width: 18px !important;
      height: 18px !important;
    }
  }
  .live-stream-icon{
    svg{
      width: 19px !important;
      height: 19px !important;
    }
  }
  .report-icon{
    svg{
      width: 19px !important;
      height: 19px !important;
    }
  }
  .full-screen-icon{
    svg{
      width: 18px !important;
      height: 18px !important;
    }
  }

  .settings-menu-inner-text {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1rem;
    width: auto;
  }
}
.setting-control-button {
  width: 11rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 3px;
}

/* Media Queries for Responsiveness */
@media (max-width: 875px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.6em;
    height: 2.8em;
  }

  .settings-menu-inner-text {
    font-size: 0.8rem;
  }

  .settings-menu-inner-icon svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 710px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.5em;
    height: 2.5em;
  }

  .settings-menu-inner-text {
    font-size: 0.7rem;
  }

  .settings-menu-inner-icon svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 450px) {
  .setting-control-button-popover {
    z-index: 11;
  }
  .setting-control-button {
    width: auto;
    .mvt-options {
      margin: 0;
      padding: 5px 10px;
    }
  }
}

.setting-control-button-mobile {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 0.5rem;
}

// Combined PiP button styles
.pip-combined-item {
  .settings-menu-inner-text {
    flex: 1;
    justify-content: flex-start;
  }
}

.auto-pip-indicator-wrapper {
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;

  .auto-pip-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 12px;
    background-color: #2a2a2a !important;
    border: 1px solid #404040 !important;
    min-width: 28px;
    height: 18px;

    .auto-pip-text {
      font-size: 10px;
      font-weight: 600;
      color: #ffffff !important;
      line-height: 1;
    }

    .auto-pip-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;

      &.enabled {
        background-color: #4ade80 !important;
        box-shadow: 0 0 4px rgba(74, 222, 128, 0.4);
      }

      &.disabled {
        background-color: #6b7280 !important;
      }
    }
  }
}
